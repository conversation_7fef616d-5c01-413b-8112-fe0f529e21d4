# Product Requirements Document (PRD): Next-Generation AI-Powered IT Helpdesk - HarmonyDesk

## 1. Purpose

Build a centralized, AI-powered IT Helpdesk web application that streamlines IT support for all staff, with a special focus on non-tech-savvy users. The system will feature dynamic, auto-populating forms, a tabbed request confirmation interface, robust role-based access, and AI-driven self-service tools—all governed by strict project policy and task-driven development.

## 2. Key Features & User Stories

### 2.1 Dynamic Modal Staff Form

- **Description:** An interactive, step-by-step modal form that adapts to the user's department, role, and request type.
- **Requirements:**
    - Auto-populate fields (e.g., staff email, PC ID, group mailbox) as soon as a staff member is selected.
    - Display only relevant service categories and staff data based on the user's department/group.
    - Validate input in real-time and provide helpful AI-driven suggestions or corrections.
- **User Story:** *As a regular staff member, I want to submit an IT request without manually entering technical info, so I can get help quickly and without errors*.

### 2.2 Tabbed Confirmation Page

- **Description:** After submitting requests, users see a confirmation page with tabs for each service category (e.g., Email, Hardware, Access), allowing review and edits before final submission.
- **Requirements:**
    - Aggregate multiple requests into a single session.
    - Allow users to review, edit, or remove requests within each tab.
    - Only submit when the user confirms all details.
- **User Story:** *As a user, I want to review all my requests in one place before submitting, so I can catch mistakes and feel confident*.

### 2.3 AI-Powered Knowledge Base & Chatbot

- **Description:** An AI chatbot and knowledge base that guides users through the request process, fetches up-to-date solutions, and empowers self-service.
- **Requirements:**
    - Integrate a chatbot that can answer common IT questions and guide users step-by-step.
    - Fetch latest knowledge base articles via web search.
    - Offer proactive troubleshooting suggestions before escalating to IT staff.
- **User Story:** *As a non-tech-savvy user, I want an AI assistant to help me solve problems myself, so I don't always have to wait for IT*.

### 2.4 Role-Based Access Control (RBAC)

- **Description:** The system must enforce strict role-based data visibility and permissions.
- **Roles & Permissions:**
    - **Global Administrator:** Full access, manage all users and settings.
    - **Web App System Administrator:** Manage users and monitor system.
    - **Department Administrator:** Manage staff/requests within their department.
    - **IT Helpdesk Support Staff:** Process and fulfill IT requests.
    - **Regular Staff:** Submit requests, use self-service tools.
    - **HR Dashboard:** Triggers onboarding/offboarding workflows.
- **User Story:** *As a Department Admin, I want to see only my department's data, so I can manage requests securely*.

### 2.5 HR Dashboard Integration

- **Description:** Integrate with the HR dashboard to automate onboarding/offboarding IT tasks.
- **Requirements:**
    - Automatically trigger IT provisioning (accounts, hardware, access) for new hires.
    - Auto-initiate deprovisioning for offboarding staff.
    - Notify relevant admins and IT staff of required actions.
- **User Story:** *As an IT staff member, I want to be notified automatically when someone joins or leaves, so I can prepare IT resources efficiently*.

### 2.6 Supabase Database Integration

- **Description:** Leverage existing Supabase database structure with proper RBAC and department isolation.
- **Requirements:**
    - Implement Row-Level Security (RLS) policies for department-based data access.
    - Use existing staff, departments, group_mail_addresses, and service_categories tables.
    - Support bilingual (Japanese/English) content management.
    - Maintain audit trails for all operations.

## 3. Technical Architecture

### 3.1 Database Structure (Supabase PostgreSQL)
- **Staff Directory:** 275 records with department associations
- **Service Categories:** 10 pre-configured request types with JSON schemas
- **Roles:** 6 roles matching RBAC requirements
- **Asset Management:** PC assets with proper M-prefix IDs
- **Group Mail & Mailboxes:** Pre-configured email resources
- **SharePoint Libraries:** 42 libraries with department associations

### 3.2 AI Integration
- Dynamic form generation using existing service category schemas
- Auto-population of staff and asset data
- Conversational assistance for non-technical users
- Predictive automation for onboarding workflows

### 3.3 Workflow Orchestration
- Multi-service request handling with tabbed confirmation
- Department-based data filtering and access control
- Real-time notifications and status tracking
- Integration hooks for external systems (Microsoft 365, Azure AD)

## 4. Core Scenarios

### Scenario 1: Single User, Multiple Requests
A staff member requests addition to multiple group mail addresses, mailbox creation, and SharePoint access for one person.

### Scenario 2: Multiple Users, Multiple Requests
A department admin requests various IT services (group mail, mailbox, PC admin privileges, SharePoint access) for multiple staff members.

### Scenario 3: Mixed Add/Remove Operations
A staff member requests both additions and removals across different services for the same user.

### Scenario 4: HR-Triggered Onboarding
HR system automatically triggers comprehensive onboarding workflow including account creation, group assignments, and asset provisioning.

## 5. Success Criteria

- Users can submit, review, and confirm multi-category requests in a single session
- Dynamic forms auto-populate and validate fields based on user input and role
- AI chatbot provides accurate, up-to-date guidance and troubleshooting
- Only authorized roles can view or act on specific data and requests
- HR-triggered workflows run automatically and notify the correct staff
- System supports 1,000+ concurrent users with offline PWA capabilities

## 6. Implementation Phases

### Phase 1: Foundation & Core Functionality
- RBAC implementation with Supabase RLS
- Basic dynamic form system for key service categories
- Department-based data filtering
- Initial AI chatbot for FAQ support

### Phase 2: Intelligence Layer
- Advanced AI form assistance and auto-population
- Workflow orchestration engine
- Knowledge base integration
- Multi-service request handling

### Phase 3: Advanced Automation & Integration
- Predictive analytics and proactive suggestions
- External system integrations (Microsoft 365, Azure AD)
- Voice interface and multi-channel support
- Advanced workflow automation

### Phase 4: Optimization & Analytics
- Performance tuning and scalability improvements
- Advanced analytics and reporting dashboards
- Security hardening and compliance features
- Long-term maintenance and support systems

## 7. Constraints & Requirements

- All development must follow strict task-driven approach with PBI mapping
- No external package integration without prior research and documentation
- Support for Japanese-first environment with English fallback
- Offline-first PWA design for vessel-based users
- Enterprise-grade security and audit compliance
- Integration readiness for future external systems

## 8. Key Metrics

- Request processing time reduction by 70%
- User satisfaction score > 4.5/5
- IT staff workload reduction by 50% through automation
- 99.9% uptime with real-time sync capabilities
- Zero security incidents with proper RBAC enforcement
