# Task ID: 1
# Title: Setup Supabase Database with RBAC
# Status: pending
# Dependencies: None
# Priority: high
# Description: Configure Supabase PostgreSQL database with proper Row-Level Security (RLS) policies for department-based data access and role-based permissions.
# Details:
1. Initialize Supabase project
2. Configure existing tables: staff, departments, group_mail_addresses, service_categories
3. Implement RLS policies for each table based on the 6 defined roles:
   - Global Administrator: Full access to all tables
   - Web App System Administrator: Access to manage users and monitor system
   - Department Administrator: Access limited to their department data
   - IT Helpdesk Support Staff: Access to process and fulfill requests
   - Regular Staff: Limited access to submit requests
   - HR Dashboard: Access to trigger workflows
4. Set up audit trails for all operations
5. Configure bilingual (Japanese/English) content management
6. Create database hooks for real-time notifications

Example RLS policy for department-based filtering:
```sql
CREATE POLICY "Users can only view their department data" ON staff
FOR SELECT USING (
  auth.uid() IN (
    SELECT id FROM users WHERE department_id = staff.department_id
  ) OR 
  auth.uid() IN (SELECT id FROM users WHERE role = 'global_admin')
);
```

# Test Strategy:
1. Unit tests for each RLS policy to verify proper access control
2. Integration tests with mock users of each role type
3. Verify audit trail captures all CRUD operations
4. Test bilingual content retrieval
5. Performance testing with simulated load of 1,000+ concurrent users
6. Security testing to ensure data isolation between departments
