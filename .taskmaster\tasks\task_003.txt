# Task ID: 3
# Title: Create Dynamic Modal Staff Form
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Develop an interactive, step-by-step modal form that adapts to the user's department, role, and request type with auto-population capabilities.
# Details:
1. Design form component architecture with step progression
2. Implement auto-population logic for staff data (email, PC ID, group mailbox)
3. Create dynamic field rendering based on service category schemas
4. Implement department/group-based filtering of service categories
5. Add real-time validation with helpful error messages
6. Integrate AI-driven suggestions for form fields
7. Create responsive design that works on all devices

Pseudo-code for auto-population:
```javascript
async function handleStaffSelection(staffId) {
  // Fetch staff details from Supabase
  const { data, error } = await supabase
    .from('staff')
    .select('email, pc_id, department_id')
    .eq('id', staffId)
    .single();
  
  if (data) {
    // Auto-populate form fields
    formState.email = data.email;
    formState.pcId = data.pc_id;
    
    // Fetch related group mailboxes
    const { data: mailboxes } = await supabase
      .from('group_mail_addresses')
      .select('*')
      .eq('department_id', data.department_id);
    
    // Update available options
    formState.availableMailboxes = mailboxes;
  }
}
```

# Test Strategy:
1. Unit tests for form validation logic
2. Integration tests for auto-population features
3. User acceptance testing with representatives from each department
4. Test form behavior with various service categories
5. Verify form adapts correctly based on user department and role
6. Performance testing for auto-population response time
7. Accessibility testing (WCAG compliance)
