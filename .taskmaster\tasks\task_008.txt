# Task ID: 8
# Title: Implement Department-Based Data Filtering
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Create a system that filters data based on user department and role, ensuring users only see relevant information.
# Details:
1. Implement data access layer with department filtering
2. Create query builders that automatically apply department filters
3. Develop UI components that adapt to filtered data
4. Implement caching strategies for frequently accessed filtered data
5. Add override capabilities for authorized roles (Global Admin, System Admin)

Example query builder with department filtering:
```javascript
class QueryBuilder {
  constructor(table, user) {
    this.table = table;
    this.user = user;
    this.query = supabase.from(table);
  }
  
  applyDepartmentFilter() {
    // Skip filter for global roles
    if (['global_admin', 'system_admin'].includes(this.user.role)) {
      return this;
    }
    
    // Apply department filter for department-scoped roles
    if (['department_admin', 'it_helpdesk', 'staff'].includes(this.user.role)) {
      this.query = this.query.eq('department_id', this.user.departmentId);
    }
    
    return this;
  }
  
  select(columns) {
    this.query = this.query.select(columns);
    return this.applyDepartmentFilter();
  }
  
  async execute() {
    return await this.query;
  }
}
```

# Test Strategy:
1. Unit tests for query building logic
2. Integration tests with users from different departments
3. Verify data isolation between departments
4. Test override capabilities for authorized roles
5. Performance testing for filtered queries
6. Test caching mechanisms
7. Verify UI components adapt correctly to filtered data
8. Security testing to ensure data isolation
