# Task ID: 13
# Title: Implement Supabase Onboarding Workflow for HR Dashboard
# Status: pending
# Dependencies: 1, 2, 12
# Priority: high
# Description: Create a streamlined onboarding process using Supabase for the HR dashboard, enabling efficient user registration and role assignment.
# Details:
1. Set up Supabase Authentication:
   - Configure email/password and SSO (Single Sign-On) options
   - Implement email verification process

2. Create onboarding UI components:
   - Registration form with required fields (name, email, department, etc.)
   - Role selection dropdown (based on predefined roles in Task 2)
   - Terms of service and privacy policy acceptance checkboxes

3. Implement backend onboarding logic:
   - Create a Supabase function to handle user registration
   - Implement role assignment based on selection and department
   - Set up initial user metadata and preferences

4. Develop approval workflow:
   - Create a queue for new user approvals
   - Implement admin interface for reviewing and approving new users
   - Set up email notifications for admins about pending approvals

5. Implement post-approval actions:
   - Trigger welcome email with login instructions
   - Set up initial dashboard view based on assigned role

6. Add security measures:
   - Implement rate limiting for registration attempts
   - Set up logging for all registration and approval actions

7. Create migration script for existing users:
   - Develop a script to migrate existing user data into Supabase
   - Implement dry-run and validation checks before actual migration

Example Supabase function for user registration:

```javascript
const { createClient } = require('@supabase/supabase-js')
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY)

exports.handler = async (event, context) => {
  const { email, password, name, department, role } = JSON.parse(event.body)

  const { user, error } = await supabase.auth.signUp({
    email,
    password,
  })

  if (error) {
    return { statusCode: 400, body: JSON.stringify({ error: error.message }) }
  }

  const { data, error: metadataError } = await supabase
    .from('user_metadata')
    .insert({ user_id: user.id, name, department, role, status: 'pending_approval' })

  if (metadataError) {
    // Rollback user creation if metadata insertion fails
    await supabase.auth.api.deleteUser(user.id)
    return { statusCode: 500, body: JSON.stringify({ error: metadataError.message }) }
  }

  // Trigger admin notification for approval

  return { statusCode: 200, body: JSON.stringify({ message: 'User registered successfully, awaiting approval' }) }
}
```

8. Implement department-based data access:
   - Utilize Row Level Security (RLS) policies created in Task 1
   - Ensure new users can only access data within their department

9. Integrate with RBAC system:
   - Connect the onboarding workflow with the RBAC system from Task 2
   - Ensure proper permissions are set upon user approval

# Test Strategy:
1. Unit test Supabase functions:
   - Test user registration with valid and invalid inputs
   - Verify role assignment logic
   - Test approval workflow functions

2. Integration tests:
   - End-to-end test of the registration process
   - Verify email notifications are sent correctly
   - Test SSO integration if implemented

3. UI/UX testing:
   - Verify all form validations work correctly
   - Test responsiveness of the onboarding UI
   - Ensure accessibility standards are met

4. Security testing:
   - Attempt to bypass email verification
   - Test rate limiting on registration attempts
   - Verify proper error handling and logging

5. Role and permission testing:
   - Register users with different roles
   - Verify users can only access appropriate data and functions based on their role
   - Test department-based data access restrictions

6. Migration script testing:
   - Perform dry-run tests with sample legacy data
   - Verify all user data is correctly migrated to Supabase
   - Test rollback procedures for failed migrations

7. Performance testing:
   - Simulate multiple concurrent registrations
   - Measure response times for registration and approval processes

8. Compliance testing:
   - Ensure all necessary user consent is captured during registration
   - Verify that data protection regulations are adhered to in the onboarding process

9. Cross-browser and device testing:
   - Test the onboarding process on various browsers and devices
   - Verify that the experience is consistent across platforms

10. Localization testing:
    - Test the onboarding process in both Japanese and English
    - Verify that all error messages and notifications are correctly translated

# Subtasks:
## 1. Set up Supabase Authentication for HR Dashboard [pending]
### Dependencies: None
### Description: Configure Supabase authentication system with email/password and SSO options for the HR dashboard
### Details:
Implement email verification process, configure OAuth providers for SSO, set up password policies, and create necessary database tables for user authentication

## 2. Develop Onboarding UI Components [pending]
### Dependencies: 13.1
### Description: Create user interface components for the onboarding process in the HR dashboard
### Details:
Design and implement registration form with fields for name, email, department, etc. Create role selection dropdown based on predefined roles. Add checkboxes for terms of service and privacy policy acceptance

## 3. Implement Backend Onboarding Logic [pending]
### Dependencies: 13.1, 13.2
### Description: Create Supabase functions to handle user registration and role assignment
### Details:
Develop Supabase function for user registration, implement role assignment based on selection and department, set up initial user metadata and preferences storage

## 4. Create Approval Workflow and Admin Interface [pending]
### Dependencies: 13.3
### Description: Develop a queue system for new user approvals and an admin interface for managing approvals
### Details:
Implement a queue for new user approvals, create an admin interface for reviewing and approving new users, set up email notifications for admins about pending approvals

## 5. Implement Post-Approval Actions [pending]
### Dependencies: 13.4
### Description: Set up automated actions to be performed after user approval
### Details:
Create a system to trigger welcome emails with login instructions, set up initial dashboard view based on assigned role, implement database triggers for status updates

## 6. Integrate RBAC and Department-Based Access Control [pending]
### Dependencies: 13.3, 13.4, 13.5
### Description: Connect the onboarding workflow with the RBAC system and implement department-based data access
### Details:
Integrate with RBAC system from Task 2, ensure proper permissions are set upon user approval, utilize Row Level Security (RLS) policies for department-based data access

## 7. Implement Real-Time Status Tracking [pending]
### Dependencies: 13.3, 13.4, 13.5, 13.6
### Description: Develop a system for real-time tracking of onboarding status across all 7 PRD scenarios
### Details:
Create a real-time status tracking system using Supabase's real-time capabilities, implement status updates for all 7 scenarios outlined in the PRD, develop UI components to display current status

## 8. Develop Migration Script for Existing Users [pending]
### Dependencies: 13.1, 13.3, 13.6
### Description: Create a script to migrate existing user data into the new Supabase-based system
### Details:
Develop a migration script to transfer existing user data into Supabase, implement dry-run and validation checks before actual migration, ensure proper role and department assignment during migration

